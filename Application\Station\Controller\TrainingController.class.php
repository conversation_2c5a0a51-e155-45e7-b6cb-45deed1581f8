<?php
namespace Station\Controller;

use Common\Controller\WstationController;

class TrainingController extends WstationController
{
    public function _initialize()
    {
        parent::_initialize();
        $this->login();

        $openid = session('openid2');
        $userRow = D("User")->where(['openid' => $openid, 'service_id' => 1])->find();
        if (!$userRow) {
            session(null);
            return $this->redirect(U('/index/index'));
            die;
        }
        $this->userRow = $userRow;

        if ($this->userRow['is_service_station'] == 0) { //注册绑定
            return $this->redirect(U('index/logins'));
            die;
        }

        $serviceStationRow = null;
        $jobmsgcount = 0;

        if ($userRow['self_service_station_id'] > 0) {
            D("service_station")->where(['id' => $userRow['self_service_station_id']])->save(['lastactive_time' => time()]);

            // 获取服务站信息
            $serviceStationRow = D("ServiceStation")
                ->where(['id' => $userRow['self_service_station_id'], 'status' => 1])
                ->find();

            // 将服务站的zsb_type字段合并到userRow中，以便后续使用
            if ($serviceStationRow) {
                $this->userRow['zsb_type'] = $serviceStationRow['zsb_type'];

                // 检查招就办是否被禁用
                if ($serviceStationRow['zsb_type'] == 2 && $serviceStationRow['is_disabled'] == 1) {
                    $this->error('您的账户已被禁用，如有疑问请联系管理员');
                }
            } else {
                $this->userRow['zsb_type'] = 1; // 默认为服务站
            }

            // 执行查询
            $jobmsgcount = D("user_job")->where(['service_station_id' => $userRow['self_service_station_id'],'need_reply' => 1])->count();
        } else {
            $this->userRow['zsb_type'] = 1; // 默认为服务站
        }

        // 确保变量始终被设置
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('jobmsgcount', $jobmsgcount ?: 0);

        //公告管理
        $adList = D("PageAd")->where(['status' => 1])->order('id desc')->select();
        $this->assign('adList', $adList);
        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('userRow', $userRow);

        // 权限安全检查
        if (!$this->checkUserPermission('view')) {
            \Think\Log::write('培训管理访问被拒绝：用户无权限 user_id=' . $this->userRow['id'], 'WARN');
            $this->error('无权限访问培训管理');
        }

        // 数据安全检查：验证用户会话完整性
        if (!$this->validateUserSession()) {
            \Think\Log::write('培训管理访问被拒绝：用户会话异常 user_id=' . $this->userRow['id'], 'WARN');
            session(null); // 清除会话
            $this->error('会话异常，请重新登录');
        }

        // 记录访问日志
        $this->logUserAccess();

        // 调试日志
        \Think\Log::write('TrainingController _initialize - userRow: ' . json_encode($userRow), 'INFO');
        if (isset($serviceStationRow)) {
            \Think\Log::write('TrainingController _initialize - serviceStationRow: ' . json_encode($serviceStationRow), 'INFO');
        } else {
            \Think\Log::write('TrainingController _initialize - serviceStationRow is not set', 'ERROR');
        }
    }

    /**
     * 培训订单列表
     */
    public function index()
    {
        // 获取用户可访问的订单查询条件
        $where = $this->getUserOrderWhere();

        // 获取筛选参数
        $s_order_type = I("get.order_type"); // 订单类型筛选
        $s_main_status = I("get.main_status");
        $s_sub_status = I("get.sub_status");
        $s_kwd = I("get.kwd");

        // 处理订单类型筛选（仅对服务站用户有效）
        if ($this->userRow['zsb_type'] != 2 && !empty($s_order_type)) {
            switch ($s_order_type) {
                case 'zsb':
                    // 只显示招就办订单：zsb_id > 0
                    $where['zsb_id'] = ['gt', 0];
                    break;
                case 'station':
                    // 只显示自有订单：zsb_id = 0 或 NULL
                    $where['_complex'] = [
                        '_logic' => 'OR',
                        ['zsb_id' => 0],
                        ['zsb_id' => ['exp', 'IS NULL']]
                    ];
                    break;
                // 'all' 或其他值：显示所有订单，不添加额外条件
            }
        }

        // 构建查询条件（使用新的二级状态）
        if ($s_main_status != '') $where['main_status'] = $s_main_status;
        if ($s_sub_status != '') $where['sub_status'] = $s_sub_status;

        // 搜索关键词 - 需要同时搜索用户表和简历表
        if ($s_kwd != '') {
            // 先获取匹配的用户ID
            $matchedUserIds = D('User')->where([
                'nickname' => ['like', '%' . $s_kwd . '%'],
                'service_station_id' => $this->userRow['self_service_station_id']
            ])->getField('id', true);

            // 再获取匹配的简历ID - 只获取已分析完成且非服务终止状态的简历
            $matchedJobIds = [];
            $matchedJobs = D('UserJob')->where([
                'name' => ['like', '%' . $s_kwd . '%'],
                'service_station_id' => $this->userRow['self_service_station_id'],
                'job_state' => ['neq', 3] // 排除服务终止状态的简历
            ])->getField('id', true);

            if (!empty($matchedJobs)) {
                // 过滤出已分析完成的简历 - 使用与list-joblist.html中相同的条件：is_html == 3
                $validJobDocs = D('UserJobDoc')->where([
                    'user_job_id' => ['in', $matchedJobs],
                    'is_html' => 3
                ])->getField('user_job_id', true);

                $matchedJobIds = $validJobDocs;
            }

            // 构建复合查询条件
            $kwdWhere = [];
            if (!empty($matchedUserIds)) {
                $kwdWhere['user_id'] = ['in', $matchedUserIds];
            }
            if (!empty($matchedJobIds)) {
                $kwdWhere['user_job_id'] = ['in', $matchedJobIds];
            }

            // 如果两个条件都有，使用OR连接
            if (!empty($matchedUserIds) && !empty($matchedJobIds)) {
                $where['_complex'] = $kwdWhere;
                $where['_logic'] = 'OR';
            } elseif (!empty($matchedUserIds)) {
                $where['user_id'] = ['in', $matchedUserIds];
            } elseif (!empty($matchedJobIds)) {
                $where['user_job_id'] = ['in', $matchedJobIds];
            } else {
                // 如果没有匹配的用户或简历，设置一个不可能满足的条件
                $where['id'] = -1;
            }
        }

        // 获取每页显示条数，默认20条
        $pageSize = I('get.psz', 20, 'intval');
        // 确保页面大小只能是10, 20, 50
        if (!in_array($pageSize, [10, 20, 50])) {
            $pageSize = 20;
        }

        // 分页
        $obj = D("TrainingOrder");
        $count = $obj->where($where)->count();
        $page = $this->page($count, $pageSize);

        // 获取订单列表
        $list = $obj->getOrderList($where, 'create_time desc', $page->firstRow, $page->listRows);

        // 重新计算招就办订单的收益（服务站收益+招就办收益）
        foreach ($list as &$item) {
            if (!empty($item['is_zsb_order']) && $item['is_zsb_order']) {
                // 使用新的计算方法重新计算总收益
                $totalProfit = $obj->calculateZjbTotalProfit($item);
                // 将总收益转换为分单位存储，保持与原有数据结构一致
                $item['station_profit'] = intval($totalProfit * 100);
            }
        }
        unset($item); // 清除引用

        // 获取创建培训订单所需数据
        if ($this->userRow['zsb_type'] == 2) {
            // 招就办用户：获取招就办下的学员列表
            $userList = D('User')->where([
                'service_station_id' => $this->userRow['self_service_station_id'],
                'service_status' => 0 // 只显示沟通中的学员
            ])->field('id, nickname as realname, mobile')->select();

            // 招就办用户：只获取已配置价格的项目
            $projectList = $this->getZjbAvailableProjects();
        } else {
            // 服务站用户：获取服务站下的学员列表
            $userList = D('User')->where([
                'service_station_id' => $this->userRow['self_service_station_id'],
                'service_status' => 0 // 只显示沟通中的学员
            ])->field('id, nickname as realname, mobile')->select();

            // 服务站用户：只获取有上架岗位的项目
            $projectList = $this->getProjectsWithAvailablePosts();
        }

        // 过滤掉已经报名培训的学员（有非closed状态的培训订单）
        if (!empty($userList)) {
            $filteredUserList = [];
            foreach ($userList as $user) {
                // 检查该学员是否已经有培训订单（除了已终止和已完成的订单）
                $existOrder = D('TrainingOrder')->where([
                    'user_id' => $user['id'],
                    'order_status' => ['not in', ['closed']],
                    'sub_status' => ['not in', ['terminated', 'completed']]
                ])->find();

                // 如果没有培训订单，则加入列表
                if (!$existOrder) {
                    $filteredUserList[] = $user;
                }
            }
            $userList = $filteredUserList;
        }

        // 模板赋值（使用新的二级状态系统）
        $this->assign('main_status_list', $obj->main_status);
        $this->assign('sub_status_list', $obj->sub_status);
        $this->assign('list', $list);
        $this->assign('pageSize', $pageSize);
        $this->assign("page", $page->show());
        $this->assign('userList', $userList);
        $this->assign('projectList', $projectList);
        $this->assign('orderType', $s_order_type); // 传递订单类型参数

        // 确保头部模板变量存在
        $serviceStationRow = D("ServiceStation")
            ->where(['id' => $this->userRow['self_service_station_id'], 'status' => 1])
            ->find();
        $this->assign('serviceStationRow', $serviceStationRow);
        $this->assign('levelList', D("ServiceStation")->level);
        $this->assign('userRow', $this->userRow);
        $this->display();
    }

    /**
     * 创建培训订单
     */
    public function create()
    {
        // 验证创建权限
        if (!$this->checkUserPermission('create')) {
            $this->error('无权限创建订单');
        }

        if (IS_POST) {
            $orderModel = D("TrainingOrder");

            // 获取岗位信息（不限制是否上架）
            $postId = I('post.post_id');
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '培训岗位不存在']);
                } else {
                    $this->error('培训岗位不存在');
                }
                return;
            }

            // 获取简历信息
            $userJobId = I('post.user_id');
            $userJob = D('UserJob')->where(['id' => $userJobId])->find();
            if (!$userJob) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '学员简历不存在']);
                } else {
                    $this->error('学员简历不存在');
                }
                return;
            }

            // 检查简历状态，服务终止状态的简历无法报名培训
            // 注意：取消报名后简历状态会恢复为0（沟通中），所以这里不会阻止重新报名
            if ($userJob['job_state'] == 3) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该简历已标记为服务终止状态，无法报名培训']);
                } else {
                    $this->error('该简历已标记为服务终止状态，无法报名培训');
                }
                return;
            }

            // 检查简历是否已分析完成 - 使用与list-joblist.html中相同的条件：is_html == 3
            $userJobDoc = D('UserJobDoc')->where(['user_job_id' => $userJobId, 'is_html' => 3])->find();
            if (!$userJobDoc) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该简历尚未分析完成，无法报名']);
                } else {
                    $this->error('该简历尚未分析完成，无法报名');
                }
                return;
            }

            // 获取或创建用户
            $user = D('User')->where(['mobile' => $userJob['phone']])->find();
            if (!$user) {
                // 如果用户不存在，则创建用户
                // 为openid生成一个唯一值，避免唯一键冲突
                $uniqueOpenid = 'job_' . $userJob['id'] . '_' . time() . '_' . mt_rand(1000, 9999);

                $userId = D('User')->add([
                    'nickname' => $userJob['name'],
                    'mobile' => $userJob['phone'],
                    'service_station_id' => $this->userRow['self_service_station_id'],
                    'service_status' => 0, // 沟通中
                    'openid' => $uniqueOpenid, // 添加唯一的openid
                    'created' => time()
                ]);
            } else {
                $userId = $user['id'];

                // **新增：检查用户服务状态**
                if ($user['service_status'] == 1) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '该学员正在培训中，无法重复报名']);
                    } else {
                        $this->error('该学员正在培训中，无法重复报名');
                    }
                    return;
                } elseif ($user['service_status'] == 2) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '该学员已入职，无法重复报名培训']);
                    } else {
                        $this->error('该学员已入职，无法重复报名培训');
                    }
                    return;
                }
            }

            // 检查是否已经报名同一岗位 - 同时检查user_id和user_job_id（排除已取消的订单）
            $existOrder = $orderModel->where([
                '_complex' => [
                    '_logic' => 'OR',
                    'user_id' => $userId,
                    'user_job_id' => $userJobId
                ],
                'post_id' => $postId,
                'order_status' => ['not in', ['closed']],
                'sub_status' => ['not in', ['terminated']] // 允许取消报名后重新报名
            ])->find();

            // 记录调试信息
            \Think\Log::write('检查是否已报名同一岗位: user_id=' . $userId . ', user_job_id=' . $userJobId . ', 结果=' . ($existOrder ? '已报名' : '未报名'), 'INFO');
            if ($existOrder) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该学员已经报名此培训岗位']);
                } else {
                    $this->error('该学员已经报名此培训岗位');
                }
                return;
            }

            // 检查学员是否已经报名了任何培训项目（除了已关闭和已取消的订单）
            $existAnyOrder = $orderModel->where([
                '_complex' => [
                    '_logic' => 'OR',
                    'user_id' => $userId,
                    'user_job_id' => $userJobId
                ],
                'order_status' => ['not in', ['closed']],
                'sub_status' => ['not in', ['terminated']] // 允许取消报名后重新报名
            ])->find();

            // 记录调试信息
            \Think\Log::write('检查是否已报名任何培训: user_id=' . $userId . ', user_job_id=' . $userJobId . ', 结果=' . ($existAnyOrder ? '已报名其他培训' : '未报名任何培训'), 'INFO');
            if ($existAnyOrder) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '该学员已经报名了培训项目，不能同时报名多个培训']);
                } else {
                    $this->error('该学员已经报名了培训项目，不能同时报名多个培训');
                }
                return;
            }

            // 获取用户输入的报名费（前端传递的是元单位）
            $feeAmount = I('post.fee_amount', 0, 'floatval');

            // 转换为分单位用于招就办价格匹配
            $feeAmountCents = intval($feeAmount * 100);

            \Think\Log::write('用户输入报名费: ' . $feeAmount . '元 = ' . $feeAmountCents . '分', 'INFO');

            // 检查是否为公益项目，如果是则强制设置报名费为0
            if ($post['is_free'] == 1) {
                $feeAmount = 0;
                $feeAmountCents = 0;
                $feeText = '公益实习';
                \Think\Log::write('公益项目，报名费设为0: post_id=' . $postId, 'INFO');
            } else {
                // 验证报名费范围
                $validationResult = $this->validateFeeRange($feeAmount, $postId);
                if (!$validationResult['success']) {
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => $validationResult['message']]);
                    } else {
                        $this->error($validationResult['message']);
                    }
                    return;
                }

                // 确定价格文本说明
                $feeText = '自定义报名费';

                // 对于招就办用户，需要特殊处理价格匹配逻辑
                if ($this->userRow['zsb_type'] == 2) {
                    // 招就办用户：匹配招就办配置的价格
                    $zsbPriceModel = D('ZsbPostPrice');
                    $priceConfig = $zsbPriceModel->where([
                        'zsb_id' => $this->userRow['self_service_station_id'],
                        'post_id' => $postId,
                        'status' => 1
                    ])->find();

                    if ($priceConfig && $feeAmountCents == $priceConfig['sale_price']) {
                        $feeText = '招就办报名费';
                    } else {
                        \Think\Log::write('招就办价格不匹配: 输入=' . $feeAmountCents . '分, 配置=' . ($priceConfig ? $priceConfig['sale_price'] : '无') . '分', 'WARN');
                        $feeText = '自定义价格';
                    }
                }
            }

            // 创建订单数据
            $data = [
                'post_id' => $postId,
                'user_id' => $userId,
                'user_job_id' => $userJobId, // 添加简历ID关联
                'station_id' => $this->userRow['self_service_station_id'],
                'fee_amount' => intval($feeAmount * 100), // 转换为分单位存储（数据库字段注释为分单位）
                'max_fee_amount' => intval($post['max_price']), // 添加最高价格，单位为元
                'fee_text' => $feeText, // 添加价格文本说明
                'max_fee_text' => $post['max_price_text'], // 添加最高价格文本说明
                'create_time' => time(),
                'update_time' => time(),
            ];

            // 记录调试信息
            \Think\Log::write('创建订单数据: post_id=' . $postId .
                ', service_price=' . $post['service_price'] .
                ', max_price=' . $post['max_price'] .
                ', fee_amount=' . $data['fee_amount'] .
                ', max_fee_amount=' . $data['max_fee_amount'], 'INFO');

            // 业务逻辑验证
            $validationResult = $this->validateOrderCreation($data, $post, $userJob);
            if (!$validationResult['success']) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => $validationResult['message']]);
                } else {
                    $this->error($validationResult['message']);
                }
                return;
            }

            // 根据简历类型创建订单（修复逻辑：应该根据简历类型而不是用户类型）
            $resumeType = isset($userJob['resume_type']) ? $userJob['resume_type'] : 1; // 默认为自有简历

            \Think\Log::write('订单创建逻辑判断: user_job_id=' . $userJobId .
                ', resume_type=' . $resumeType .
                ', current_user_zsb_type=' . $this->userRow['zsb_type'] .
                ', resume_service_station_id=' . $userJob['service_station_id'], 'INFO');

            if ($resumeType == 2) {
                // 招就办简历：需要验证权限并创建招就办订单
                $zsbId = $userJob['service_station_id'];

                // 区分两种情况：招就办用户为自己创建订单 vs 服务站用户为下属招就办创建订单
                if (isset($this->userRow['zsb_type']) && $this->userRow['zsb_type'] == 2) {
                    // 情况1：招就办用户为自己的简历创建订单
                    if ($zsbId == $this->userRow['self_service_station_id']) {
                        // 招就办用户为自己的简历创建订单，直接允许
                        \Think\Log::write('招就办用户为自己的简历创建订单: zsb_id=' . $zsbId, 'INFO');
                        $zsbInfo = D("ServiceStation")->where([
                            'id' => $zsbId,
                            'zsb_type' => 2,
                            'status' => 1
                        ])->find();

                        if (!$zsbInfo) {
                            \Think\Log::write('招就办信息验证失败: zsb_id=' . $zsbId, 'WARN');
                            if (IS_AJAX) {
                                $this->ajaxReturn(['status' => 0, 'info' => '招就办信息异常，无法报名']);
                            } else {
                                $this->error('招就办信息异常，无法报名');
                            }
                            return;
                        }
                    } else {
                        // 招就办用户尝试为其他招就办的简历创建订单，不允许
                        \Think\Log::write('权限验证失败: 招就办用户无权为其他招就办简历报名 zsb_id=' . $zsbId .
                            ', current_station_id=' . $this->userRow['self_service_station_id'], 'WARN');
                        if (IS_AJAX) {
                            $this->ajaxReturn(['status' => 0, 'info' => '无权限为其他招就办简历报名培训']);
                        } else {
                            $this->error('无权限为其他招就办简历报名培训');
                        }
                        return;
                    }
                } else {
                    // 情况2：服务站用户为下属招就办的简历创建订单
                    $zsbInfo = D("ServiceStation")->where([
                        'id' => $zsbId,
                        'zsb_ref_station' => $this->userRow['self_service_station_id'],
                        'zsb_type' => 2,
                        'status' => 1
                    ])->find();

                    if (!$zsbInfo) {
                        \Think\Log::write('权限验证失败: 招就办不属于当前服务站 zsb_id=' . $zsbId .
                            ', current_station_id=' . $this->userRow['self_service_station_id'], 'WARN');
                        if (IS_AJAX) {
                            $this->ajaxReturn(['status' => 0, 'info' => '无权限为该招就办简历报名培训']);
                        } else {
                            $this->error('无权限为该招就办简历报名培训');
                        }
                        return;
                    }
                }

                // 验证招就办是否有该岗位的价格配置
                $hasPermission = $this->validateZjbPostPermission($postId, $zsbId);
                if (!$hasPermission) {
                    \Think\Log::write('价格配置验证失败: 招就办未配置该岗位价格 zsb_id=' . $zsbId .
                        ', post_id=' . $postId, 'WARN');
                    if (IS_AJAX) {
                        $this->ajaxReturn(['status' => 0, 'info' => '该招就办未配置此岗位价格，无法报名']);
                    } else {
                        $this->error('该招就办未配置此岗位价格，无法报名');
                    }
                    return;
                }

                // 将station_id设置为简历所属的招就办ID
                $data['station_id'] = $zsbId;

                \Think\Log::write('创建招就办订单: 权限验证通过, zsb_id=' . $zsbId, 'INFO');
                $orderId = $orderModel->createZjbOrder($data);
            } else {
                // 自有简历：创建普通订单
                \Think\Log::write('创建普通订单: 简历属于服务站, station_id=' . $data['station_id'], 'INFO');
                $orderId = $orderModel->createOrder($data);
            }

            if ($orderId) {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 1, 'info' => '报名成功', 'url' => U('training/detail', ['id' => $orderId])]);
                } else {
                    $this->success('报名成功', U('training/detail', ['id' => $orderId]));
                }
            } else {
                if (IS_AJAX) {
                    $this->ajaxReturn(['status' => 0, 'info' => '报名失败']);
                } else {
                    $this->error('报名失败');
                }
            }
        } else {
            // 获取有上架岗位的项目列表
            $projectList = $this->getProjectsWithAvailablePosts();
            $this->assign('project_list', $projectList);

            // 获取服务站下的学员列表
            $userList = D('User')->where([
                'service_station_id' => $this->userRow['self_service_station_id'],
                'service_status' => 0 // 只显示沟通中的学员
            ])->select();

            // 过滤掉已经报名培训的学员（有非closed状态的培训订单）
            if (!empty($userList)) {
                $filteredUserList = [];
                foreach ($userList as $user) {
                    // 检查该学员是否已经有培训订单（除了已关闭的订单）
                    $existOrder = D('TrainingOrder')->where([
                        'user_id' => $user['id'],
                        'order_status' => ['not in', ['closed']]
                    ])->find();

                    // 如果没有培训订单，则加入列表
                    if (!$existOrder) {
                        $filteredUserList[] = $user;
                    }
                }
                $userList = $filteredUserList;
            }

            $this->assign('user_list', $userList);

            // 如果从简历页面传递了用户ID，则预选该用户
            $userId = I('get.user_id', 0);
            if ($userId) {
                $user = D('User')->where(['id' => $userId])->find();
                if ($user) {
                    $this->assign('selected_user_id', $userId);
                }
            }

            $this->display();
        }
    }

    /**
     * 获取表单数据
     */
    public function getFormData()
    {
        // 从简历获取学员列表
        // 注意：这里只筛选简历状态，不考虑User表中是否存在对应用户
        // 在create方法中，如果用户不存在，会自动创建用户

        $currentStationId = $this->userRow['self_service_station_id'];

        // 构建查询条件：包括自有简历和下属招就办简历
        $whereCondition = [
            'job_state' => 0 // 只显示沟通中的简历
        ];

        // 获取下属招就办ID列表（包括禁用的招就办，因为需要查看历史简历）
        $zsbIds = D("ServiceStation")->where([
            'zsb_ref_station' => $currentStationId,
            'zsb_type' => 2,
            'status' => 1
            // 移除 is_disabled 条件，允许查看禁用招就办的简历
        ])->getField('id', true);

        // 构建service_station_id的查询条件
        $stationIds = [$currentStationId]; // 包含自己的服务站ID
        if (!empty($zsbIds)) {
            $stationIds = array_merge($stationIds, $zsbIds); // 包含下属招就办ID
        }

        $whereCondition['service_station_id'] = ['in', $stationIds];

        $userJobList = D('UserJob')->where($whereCondition)
            ->field('id, name as realname, phone as mobile, job_state, resume_type, service_station_id')
            ->select();

        \Think\Log::write('查询条件: ' . json_encode($whereCondition) . ', 下属招就办IDs: ' . json_encode($zsbIds), 'INFO');

        // 记录初始简历列表
        \Think\Log::write('初始简历列表: ' . json_encode($userJobList), 'INFO');

        // 不再查询User表，直接处理所有简历
        $filteredUserJobList = $userJobList;

        \Think\Log::write('过滤后的简历列表: ' . json_encode($filteredUserJobList), 'INFO');

        // 获取简历对应的文档状态，只显示已分析完成的简历
        $userJobIds = array_column($filteredUserJobList, 'id');
        \Think\Log::write('简历ID列表: ' . json_encode($userJobIds), 'INFO');

        $userJobDocList = [];

        if (!empty($userJobIds)) {
            // 使用与list-joblist.html中相同的条件：is_html == 3
            $userJobDocList = D('UserJobDoc')->where([
                'user_job_id' => ['in', $userJobIds],
                'is_html' => 3 // 只获取已分析完成的简历，与简历管理页面的"报名培训"按钮显示逻辑一致
            ])->getField('user_job_id, id, is_html', true);

            \Think\Log::write('简历文档状态列表: ' . json_encode($userJobDocList), 'INFO');
        }

        // 过滤出已分析完成的简历对应的学员
        $filteredUsers = [];
        foreach ($filteredUserJobList as $user) {
            \Think\Log::write('检查简历是否已分析: 简历ID=' . $user['id'] . ', 是否存在=' . (isset($userJobDocList[$user['id']]) ? '是' : '否'), 'INFO');

            if (isset($userJobDocList[$user['id']])) {
                // 检查该学员是否已经有培训订单（除了已关闭的订单）
                $hasTrainingOrder = false;

                // 1. 检查通过user_job_id关联的订单（排除已取消的订单）
                $existOrderByJobId = D('TrainingOrder')->where([
                    'user_job_id' => $user['id'],
                    'order_status' => ['not in', ['closed']],
                    'sub_status' => ['not in', ['terminated']] // 允许取消报名后重新报名
                ])->find();

                if ($existOrderByJobId) {
                    $hasTrainingOrder = true;
                    \Think\Log::write('简历ID=' . $user['id'] . ' 已有培训订单(通过user_job_id关联): 订单ID=' . $existOrderByJobId['id'], 'INFO');
                }

                // 2. 如果没有通过user_job_id找到订单，再检查通过手机号匹配的用户订单
                if (!$hasTrainingOrder && !empty($user['mobile'])) {
                    $userInfo = D('User')->where(['mobile' => $user['mobile']])->find();
                    if ($userInfo) {
                        $existOrderByUserId = D('TrainingOrder')->where([
                            'user_id' => $userInfo['id'],
                            'order_status' => ['not in', ['closed']]
                        ])->find();

                        if ($existOrderByUserId) {
                            $hasTrainingOrder = true;
                            \Think\Log::write('简历ID=' . $user['id'] . ' 对应用户ID=' . $userInfo['id'] . ' 已有培训订单: 订单ID=' . $existOrderByUserId['id'], 'INFO');
                        }
                    }
                }

                // 只有没有培训订单的学员才加入列表
                if (!$hasTrainingOrder) {
                    // 添加状态信息，方便前端显示
                    $user['job_state_text'] = '沟通中';

                    // 添加简历类型信息，方便前端判断显示什么界面
                    $user['resume_type'] = isset($user['resume_type']) ? $user['resume_type'] : 1; // 默认为自有简历
                    $user['resume_type_text'] = $user['resume_type'] == 2 ? '招就办简历' : '自有简历';

                    $filteredUsers[] = $user;
                    \Think\Log::write('简历ID=' . $user['id'] . ' 没有培训订单，加入列表，简历类型=' . $user['resume_type'], 'INFO');
                } else {
                    \Think\Log::write('简历ID=' . $user['id'] . ' 已有培训订单，从列表中排除', 'INFO');
                }
            }
        }

        \Think\Log::write('最终筛选的用户列表: ' . json_encode($filteredUsers), 'INFO');

        // 获取项目列表
        if ($this->userRow['zsb_type'] == 2) {
            // 招就办用户：只获取已配置价格的项目
            $projectList = $this->getZjbAvailableProjects();
        } else {
            // 服务站用户：只获取有上架岗位的项目
            $projectList = $this->getProjectsWithAvailablePosts();
        }
        \Think\Log::write('项目列表: ' . json_encode($projectList), 'INFO');

        $returnData = [
            'status' => 1,
            'data' => [
                'users' => $filteredUsers,
                'projects' => $projectList
            ]
        ];

        \Think\Log::write('返回前端数据: ' . json_encode($returnData), 'INFO');

        $this->ajaxReturn($returnData);
    }

    /**
     * 获取项目下的岗位
     */
    public function getProjectPosts()
    {
        // 记录请求信息到日志
        \Think\Log::write('getProjectPosts方法被调用，参数：' . json_encode($_GET), 'INFO');
        \Think\Log::write('当前用户信息：zsb_type=' . $this->userRow['zsb_type'] . ', station_id=' . $this->userRow['self_service_station_id'], 'INFO');

        $projectId = I('get.project_id');
        if (!$projectId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数错误']);
        }

        if ($this->userRow['zsb_type'] == 2) {
            // 招就办用户：只获取已配置价格的岗位
            \Think\Log::write('执行招就办用户逻辑，调用getZjbAvailablePosts', 'INFO');
            $posts = $this->getZjbAvailablePosts($projectId);
        } else {
            // 服务站用户：获取所有岗位
            \Think\Log::write('执行服务站用户逻辑，获取所有岗位', 'INFO');
            $posts = D('ProjectPost')->where([
                'project_id' => $projectId,
                'status' => 1
            ])->field('id, job_name as name, service_price, service_price_text, max_price, max_price_text, is_free')->select();
        }

        // 处理价格，确保前端显示单位为元
        foreach ($posts as &$post) {
            if ($this->userRow['zsb_type'] != 2) {
                // 服务站用户：使用原始价格
                if ($post['is_free'] == 1) {
                    // 公益项目：所有价格设为0
                    $post['service_price'] = 0;
                    $post['max_price'] = 0;
                    $post['service_price_in_cents'] = 0;
                    $post['max_price_in_cents'] = 0;
                    $post['service_price_formatted'] = '0.00';
                    $post['max_price_formatted'] = '0.00';
                    $post['service_price_text'] = '公益实习';
                    $post['max_price_text'] = '公益实习';
                } else {
                    // 非公益项目：正常处理价格
                    $post['service_price_in_cents'] = $post['service_price'];
                    $post['max_price_in_cents'] = $post['max_price'];
                    $post['service_price_formatted'] = number_format($post['service_price'], 2);
                    $post['max_price_formatted'] = number_format($post['max_price'], 2);
                }
            }
            // 招就办用户的价格已经在getZjbAvailablePosts中处理完毕，无需重复处理

            // 兼容旧代码，设置price字段
            $post['price'] = $post['service_price'];
            $post['price_in_cents'] = $post['service_price_in_cents'];
        }

        // 记录查询结果到日志
        \Think\Log::write('查询结果：' . json_encode($posts), 'INFO');

        $this->ajaxReturn(['status' => 1, 'data' => $posts]);
    }

    /**
     * 获取学员简历信息
     */
    public function getUserJobInfo()
    {
        $userJobId = I('get.user_job_id');
        if (!$userJobId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '参数错误']);
        }

        // 获取学员简历信息
        $userJob = D('UserJob')->where(['id' => $userJobId])->field('id, name, education_level, major')->find();
        if (!$userJob) {
            $this->ajaxReturn(['status' => 0, 'msg' => '学员简历不存在']);
        }

        $this->ajaxReturn(['status' => 1, 'data' => $userJob]);
    }

    /**
     * 订单详情
     */
    public function detail()
    {
        $id = intval(I('get.id'));
        if (!$id) $this->error('参数错误');

        // 获取订单信息
        $orderModel = D("TrainingOrder");
        $order = $orderModel->where(['id' => $id])->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        // 验证访问权限
        if (!$this->checkOrderPermission($order, 'view')) {
            \Think\Log::write('订单详情访问被拒绝：无权限 user_id=' . $this->userRow['id'] . ', order_id=' . $id, 'WARN');
            $this->error('无权查看此订单');
        }

        // 获取关联信息
        $user = D('User')->where(['id' => $order['user_id']])->find();

        // 如果订单关联了简历，优先使用简历信息
        if (!empty($order['user_job_id'])) {
            $userJob = D('UserJob')->where(['id' => $order['user_job_id']])->find();
            if ($userJob) {
                // 合并简历信息到用户信息中
                $user['realname'] = $userJob['name'];
                $user['mobile'] = $userJob['phone'];
                $user['idcard'] = $userJob['id_number'];
                // 添加简历信息标记
                $user['from_resume'] = true;
            }
        }

        $post = D('ProjectPost')->where(['id' => $order['post_id']])->find();

        // 如果是招就办订单，需要获取招就办配置的价格信息
        if ($post && !empty($order['zsb_id'])) {
            $zsbPriceConfig = D('ZsbPostPrice')->where([
                'zsb_id' => $order['zsb_id'],
                'post_id' => $order['post_id'],
                'status' => 1
            ])->find();

            if ($zsbPriceConfig) {
                // 使用招就办配置的价格
                if ($post['is_free'] == 1) {
                    // 公益项目
                    $post['service_price'] = 0;
                    $post['max_price'] = 0;
                    $post['service_price_text'] = '公益实习';
                    $post['max_price_text'] = '公益实习';
                } else {
                    // 招就办配置价格（从分转换为元）
                    $salePriceYuan = round($zsbPriceConfig['sale_price'] / 100, 2);

                    // 对于招就办订单，只显示一个价格（对外价格）
                    $post['service_price'] = $salePriceYuan;
                    $post['service_price_text'] = '';

                    // 将max_price设为0，避免显示重复价格
                    $post['max_price'] = 0;
                    $post['max_price_text'] = '';
                }

                \Think\Log::write('招就办订单价格处理: order_id=' . $order['id'] .
                    ', post_id=' . $post['id'] . ', sale_price=' . $post['service_price'] . '元', 'INFO');
            }
        }

        // 确保岗位名称和价格字段存在
        if ($post) {
            $post['name'] = $post['job_name']; // 确保name字段存在
            $post['price'] = $post['service_price']; // 确保price字段存在

            // 确保价格文本字段存在（仅对非招就办订单或未配置价格的情况）
            if (empty($order['zsb_id']) || empty($zsbPriceConfig)) {
                if (!isset($post['service_price_text']) || empty($post['service_price_text'])) {
                    $post['service_price_text'] = '本科及以上学历';
                }
                if (!isset($post['max_price_text']) || empty($post['max_price_text'])) {
                    $post['max_price_text'] = '本科以下学历';
                }
            }
        }
        $project = D('Project')->where(['id' => $post['project_id']])->find();

        // 获取招就办信息（如果是招就办订单）
        $zsbInfo = null;
        if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
            $zsbInfo = D('ServiceStation')->where(['id' => $order['zsb_id']])->find();
        }

        // 获取支付记录
        $paymentRecords = D('PaymentRecord')->getRecordsByOrderId($id);

        // 根据用户身份和订单类型设置奖励显示信息
        $realStationProfit = 0; // 真正的服务站收益
        if ($this->userRow['zsb_type'] == 2) {
            // 招就办用户显示招就办奖励
            $rewardLabel = '招就办奖励';
            $rewardLabelSuffix = '';
            $rewardAmount = !empty($order['zsb_commission']) ? round($order['zsb_commission'] / 100, 2) : 0.00;
        } else {
            // 服务站用户显示服务站奖励
            if (!empty($order['zsb_id']) && $order['zsb_id'] > 0) {
                // 招就办订单：使用新的计算方法（服务站收益+招就办收益）
                $rewardLabel = '服务站入账';
                $rewardLabelSuffix = '（含招就办佣金）';
                $orderModel = D("TrainingOrder");
                $rewardAmount = $orderModel->calculateZjbTotalProfit($order);

                // 计算真正的服务站收益
                $realStationProfit = $this->calculateRealStationProfit($order);
            } else {
                // 自有订单：使用传统奖励体系（从分单位转换为元单位显示）
                $rewardLabel = '服务站奖励';
                $rewardLabelSuffix = '';
                $rewardAmount = !empty($order['reward_station_amt']) ? round($order['reward_station_amt'] / 100, 2) : 0.00;
            }
        }

        // 获取分阶段付款金额
        $paymentAmounts = D('PaymentRecord')->getOrderPaymentAmounts($id);

        // 将付款金额合并到订单数据中
        $order = array_merge($order, $paymentAmounts);

        // 模板赋值
        $this->assign('order', $order);
        $this->assign('user', $user);
        $this->assign('post', $post);
        $this->assign('project', $project);
        $this->assign('zsb_info', $zsbInfo);
        $this->assign('zsb_price_config', isset($zsbPriceConfig) ? $zsbPriceConfig : null); // 传递招就办价格配置
        $this->assign('payment_records', $paymentRecords);

        // 只使用新的二级状态信息
        $this->assign('main_status', $orderModel->main_status);
        $this->assign('sub_status', $orderModel->sub_status);

        $this->assign('rewardLabel', $rewardLabel);
        $this->assign('rewardLabelSuffix', $rewardLabelSuffix);
        $this->assign('rewardAmount', $rewardAmount);
        $this->assign('realStationProfit', $realStationProfit); // 真正的服务站收益
        $this->assign('userRow', $this->userRow); // 添加用户信息传递

        $this->display();
    }

    /**
     * 获取招就办可用的项目列表（已配置价格的项目）
     * @return array
     */
    private function getZjbAvailableProjects()
    {
        $zsbId = $this->userRow['self_service_station_id'];

        // 获取已配置价格的岗位ID
        $configuredPostIds = D('ZsbPostPrice')->where([
            'zsb_id' => $zsbId,
            'status' => 1
        ])->getField('post_id', true);

        if (empty($configuredPostIds)) {
            return [];
        }

        // 获取这些岗位对应的项目
        $projectIds = D('ProjectPost')->where([
            'id' => ['in', $configuredPostIds],
            'status' => 1
        ])->getField('project_id', true);

        if (empty($projectIds)) {
            return [];
        }

        // 获取项目信息
        return D('Project')->where([
            'id' => ['in', array_unique($projectIds)],
            'status' => 1
        ])->field('id, name')->select();
    }

    /**
     * 获取招就办在指定项目下可用的岗位列表（已配置价格的岗位）
     * @param int $projectId 项目ID
     * @return array
     */
    private function getZjbAvailablePosts($projectId)
    {
        $zsbId = $this->userRow['self_service_station_id'];

        // 获取项目下已配置价格的岗位，包含完整的价格配置信息
        $zsbPriceModel = D('ZsbPostPrice');
        $configuredPosts = $zsbPriceModel->alias('zpp')
            ->join('LEFT JOIN __PROJECT_POST__ pp ON zpp.post_id = pp.id')
            ->where([
                'zpp.zsb_id' => $zsbId,
                'zpp.status' => 1,
                'pp.project_id' => $projectId,
                'pp.status' => 1
            ])
            ->field('pp.id, pp.job_name as name, pp.is_free,
                     zpp.cost_price, zpp.sale_price, zpp.commission')
            ->select();

        \Think\Log::write('招就办获取岗位列表: zsb_id=' . $zsbId . ', project_id=' . $projectId . ', 找到' . count($configuredPosts) . '个岗位', 'INFO');

        // 处理价格显示
        foreach ($configuredPosts as &$post) {
            if ($post['is_free'] == 1) {
                // 公益项目
                $post['service_price'] = 0;
                $post['max_price'] = 0;
                $post['service_price_text'] = '公益实习';
                $post['max_price_text'] = '公益实习';
                $post['service_price_formatted'] = '0.00';
                $post['max_price_formatted'] = '0.00';
                $post['service_price_in_cents'] = 0;
                $post['max_price_in_cents'] = 0;
            } else {
                // 招就办配置价格（从分转换为元）
                $salePriceYuan = round($post['sale_price'] / 100, 2);

                // 对于招就办用户，只显示一个价格（对外价格）
                $post['service_price'] = $salePriceYuan;
                $post['service_price_text'] = '招就办报名费';
                $post['service_price_formatted'] = number_format($salePriceYuan, 2);
                $post['service_price_in_cents'] = $post['sale_price']; // 保持分单位

                // 将max_price设为0，避免显示重复价格
                $post['max_price'] = 0;
                $post['max_price_text'] = '';
                $post['max_price_formatted'] = '0.00';
                $post['max_price_in_cents'] = 0;

                \Think\Log::write('招就办岗位价格处理: post_id=' . $post['id'] .
                    ', sale_price=' . $salePriceYuan . '元', 'INFO');
            }

            // 兼容字段（使用对外价格作为默认价格）
            $post['price'] = $post['service_price'];
            $post['price_in_cents'] = $post['service_price_in_cents'];
        }

        return $configuredPosts;
    }

    /**
     * 验证用户权限
     * @param string $action 操作类型：view, create, manage, admin
     * @return bool
     */
    private function checkUserPermission($action = 'view')
    {
        // 基础权限验证：必须是已审核通过的用户
        if ($this->userRow['is_service_station'] != 2) {
            \Think\Log::write('权限验证失败：用户未通过审核 user_id=' . $this->userRow['id'], 'WARN');
            return false;
        }

        // 获取当前服务站信息
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation) {
            \Think\Log::write('权限验证失败：服务站不存在或已禁用 station_id=' . $this->userRow['self_service_station_id'], 'WARN');
            return false;
        }

        // 检查招就办是否被禁用
        if ($currentStation['zsb_type'] == 2 && $currentStation['is_disabled'] == 1) {
            \Think\Log::write('权限验证失败：招就办已被禁用 station_id=' . $this->userRow['self_service_station_id'], 'WARN');
            return false;
        }

        switch ($action) {
            case 'view':
                // 所有已审核用户都可以查看（自己的数据），但禁用的招就办除外
                return true;
            case 'create':
                // 招就办和服务站都可以创建订单（报名），但禁用的招就办除外
                return in_array($currentStation['zsb_type'], [1, 2]);
            case 'manage':
                // 只有服务站用户可以管理订单
                return $currentStation['zsb_type'] == 1;
            case 'admin':
                // 只有服务站用户可以执行管理员操作
                return $currentStation['zsb_type'] == 1;
            default:
                \Think\Log::write('权限验证失败：未知操作类型 action=' . $action, 'WARN');
                return false;
        }
    }

    /**
     * 验证订单访问权限
     * @param array $order 订单信息
     * @param string $operation 操作类型：view, edit, delete
     * @return bool
     */
    private function checkOrderPermission($order, $operation = 'view')
    {
        if (empty($order)) {
            \Think\Log::write('订单权限验证失败：订单信息为空', 'WARN');
            return false;
        }

        // 获取当前用户的服务站信息
        $currentStation = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$currentStation) {
            \Think\Log::write('订单权限验证失败：当前服务站不存在 station_id=' . $this->userRow['self_service_station_id'], 'WARN');
            return false;
        }

        if ($currentStation['zsb_type'] == 2) {
            // 招就办用户权限验证
            $hasAccess = !empty($order['zsb_id']) && $order['zsb_id'] == $this->userRow['self_service_station_id'];

            if (!$hasAccess) {
                \Think\Log::write('招就办权限验证失败：无权访问订单 zsb_id=' . $this->userRow['self_service_station_id'] . ', order_zsb_id=' . $order['zsb_id'], 'WARN');
                return false;
            }

            // 招就办只能查看，不能编辑或删除
            if ($operation !== 'view') {
                \Think\Log::write('招就办权限验证失败：无权执行操作 operation=' . $operation, 'WARN');
                return false;
            }

            return true;
        } else {
            // 服务站用户权限验证
            $hasAccess = false;

            // 检查是否为自有订单
            if ($order['station_id'] == $this->userRow['self_service_station_id']) {
                $hasAccess = true;
            }
            // 检查是否为下属招就办的订单
            elseif (!empty($order['zsb_id'])) {
                $zsb = D("ServiceStation")->where([
                    'id' => $order['zsb_id'],
                    'zsb_ref_station' => $this->userRow['self_service_station_id'],
                    'zsb_type' => 2,
                    'status' => 1
                ])->find();

                if ($zsb) {
                    $hasAccess = true;
                    // 对于招就办订单，服务站也只能查看
                    if ($operation !== 'view') {
                        \Think\Log::write('服务站权限验证失败：对招就办订单无编辑权限 operation=' . $operation, 'WARN');
                        return false;
                    }
                }
            }

            if (!$hasAccess) {
                \Think\Log::write('服务站权限验证失败：无权访问订单 station_id=' . $this->userRow['self_service_station_id'] . ', order_station_id=' . $order['station_id'] . ', order_zsb_id=' . $order['zsb_id'], 'WARN');
            }

            return $hasAccess;
        }
    }

    /**
     * 获取用户可访问的订单查询条件
     * @return array
     */
    private function getUserOrderWhere()
    {
        if ($this->userRow['zsb_type'] == 2) {
            // 招就办用户：只能查看自己的订单
            return ['zsb_id' => $this->userRow['self_service_station_id']];
        } else {
            // 服务站用户：查看自己服务站的订单
            return ['station_id' => $this->userRow['self_service_station_id']];
        }
    }

    /**
     * 验证订单创建的业务逻辑
     * @param array $orderData 订单数据
     * @param array $post 岗位信息
     * @param array $userJob 用户简历信息
     * @return array
     */
    private function validateOrderCreation($orderData, $post, $userJob)
    {
        try {
            // 1. 验证岗位状态（移除上架状态限制）
            // 岗位存在即可，不限制是否上架

            // 2. 验证用户简历状态
            if ($userJob['job_state'] == 3) {
                return ['success' => false, 'message' => '该简历已标记为服务终止状态，无法报名培训'];
            }

            // 3. 验证招就办特殊逻辑
            if ($this->userRow['zsb_type'] == 2) {
                // 验证招就办是否有权限报名此岗位
                $hasPermission = $this->validateZjbPostPermission($post['id']);
                if (!$hasPermission) {
                    return ['success' => false, 'message' => '该岗位未配置招就办价格，无法报名'];
                }

                // 验证招就办状态
                $zsbInfo = D("ServiceStation")->where([
                    'id' => $this->userRow['self_service_station_id'],
                    'zsb_type' => 2,
                    'status' => 1
                ])->find();

                if (!$zsbInfo) {
                    return ['success' => false, 'message' => '招就办信息异常，无法报名'];
                }

                // 验证招就办是否属于有效的服务站
                if (empty($zsbInfo['zsb_ref_station'])) {
                    return ['success' => false, 'message' => '招就办未关联服务站，无法报名'];
                }

                $parentStation = D("ServiceStation")->where([
                    'id' => $zsbInfo['zsb_ref_station'],
                    'status' => 1
                ])->find();

                if (!$parentStation) {
                    return ['success' => false, 'message' => '关联的服务站不存在或已禁用，无法报名'];
                }
            }

            // 4. 验证价格信息
            if (!$post['is_free'] && $orderData['fee_amount'] <= 0) {
                return ['success' => false, 'message' => '非公益项目必须设置报名费'];
            }

            // 5. 验证并发安全
            $concurrentCheck = $this->checkConcurrentOrderCreation($orderData['user_id'], $orderData['user_job_id'], $orderData['post_id']);
            if (!$concurrentCheck['success']) {
                return $concurrentCheck;
            }

            return ['success' => true, 'message' => '验证通过'];

        } catch (\Exception $e) {
            \Think\Log::write('订单创建验证异常：' . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => '系统异常，请稍后重试'];
        }
    }

    /**
     * 验证招就办对岗位的报名权限
     * @param int $postId 岗位ID
     * @param int|null $zsbId 招就办ID，如果不传则使用当前用户的服务站ID
     * @return bool
     */
    private function validateZjbPostPermission($postId, $zsbId = null)
    {
        if ($zsbId === null) {
            $zsbId = $this->userRow['self_service_station_id'];
        }

        $priceConfig = D('ZsbPostPrice')->where([
            'zsb_id' => $zsbId,
            'post_id' => $postId,
            'status' => 1
        ])->find();

        return !empty($priceConfig);
    }

    /**
     * 检查并发订单创建安全
     * @param int $userId 用户ID
     * @param int $userJobId 简历ID
     * @param int $postId 岗位ID
     * @return array
     */
    private function checkConcurrentOrderCreation($userId, $userJobId, $postId)
    {
        // 使用数据库锁防止并发创建重复订单
        $lockKey = "order_creation_{$userId}_{$userJobId}_{$postId}";

        // 检查是否已存在相同订单（最近5分钟内创建的，排除已取消的订单）
        $recentOrder = D('TrainingOrder')->where([
            '_complex' => [
                '_logic' => 'OR',
                'user_id' => $userId,
                'user_job_id' => $userJobId
            ],
            'post_id' => $postId,
            'create_time' => ['gt', time() - 300], // 5分钟内
            'order_status' => ['not in', ['closed']],
            'sub_status' => ['not in', ['terminated']] // 允许取消报名后重新报名
        ])->find();

        if ($recentOrder) {
            return ['success' => false, 'message' => '请勿重复提交，该学员已报名此培训岗位'];
        }

        return ['success' => true, 'message' => '并发检查通过'];
    }

    /**
     * 验证用户会话完整性
     * @return bool
     */
    private function validateUserSession()
    {
        // 检查必要的用户信息是否完整
        if (empty($this->userRow['id']) || empty($this->userRow['self_service_station_id'])) {
            return false;
        }

        // 验证服务站信息是否存在且有效
        $station = D("ServiceStation")->where([
            'id' => $this->userRow['self_service_station_id'],
            'status' => 1
        ])->find();

        if (!$station) {
            return false;
        }

        // 验证用户状态
        if ($this->userRow['is_service_station'] != 2) {
            return false;
        }

        return true;
    }

    /**
     * 记录用户访问日志
     */
    private function logUserAccess()
    {
        $logData = [
            'user_id' => $this->userRow['id'],
            'station_id' => $this->userRow['self_service_station_id'],
            'controller' => CONTROLLER_NAME,
            'action' => ACTION_NAME,
            'ip' => get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'access_time' => time()
        ];

        // 这里可以记录到专门的访问日志表，目前先记录到系统日志
        \Think\Log::write('培训管理访问记录：' . json_encode($logData), 'INFO');
    }

    /**
     * 获取招就办岗位价格配置
     */
    public function getZsbPostPrice()
    {
        $postId = I('get.post_id', 0, 'intval');
        $zsbId = I('get.zsb_id', 0, 'intval'); // 支持传入招就办ID

        if (!$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位ID不能为空']);
        }

        // 确定使用哪个招就办ID
        if ($zsbId > 0) {
            // 如果传入了招就办ID，需要区分两种情况
            if (isset($this->userRow['zsb_type']) && $this->userRow['zsb_type'] == 2) {
                // 情况1：招就办用户访问价格
                if ($zsbId == $this->userRow['self_service_station_id']) {
                    // 招就办用户访问自己的价格，直接允许
                    $targetZsbId = $zsbId;
                    \Think\Log::write('getZsbPostPrice调用 - 招就办用户访问自己的价格: zsb_id=' . $zsbId, 'INFO');
                } else {
                    // 招就办用户尝试访问其他招就办的价格，不允许
                    $this->ajaxReturn(['status' => 0, 'msg' => '无权限访问其他招就办的价格']);
                }
            } else {
                // 情况2：服务站用户访问下属招就办的价格
                $zsbInfo = D("ServiceStation")->where([
                    'id' => $zsbId,
                    'zsb_ref_station' => $this->userRow['self_service_station_id'],
                    'zsb_type' => 2,
                    'status' => 1
                ])->find();

                if (!$zsbInfo) {
                    $this->ajaxReturn(['status' => 0, 'msg' => '无权限访问该招就办']);
                }

                $targetZsbId = $zsbId;
                \Think\Log::write('getZsbPostPrice调用 - 服务站用户访问招就办价格: zsb_id=' . $zsbId, 'INFO');
            }
        } else {
            // 没有传入招就办ID，使用当前用户的服务站ID（仅限招就办用户）
            if (!isset($this->userRow['zsb_type']) || $this->userRow['zsb_type'] != 2) {
                $this->ajaxReturn(['status' => 0, 'msg' => '无权限访问 - 用户类型: ' . (isset($this->userRow['zsb_type']) ? $this->userRow['zsb_type'] : '未设置')]);
            }

            $targetZsbId = $this->userRow['self_service_station_id'];
            \Think\Log::write('getZsbPostPrice调用 - 招就办用户访问自己的价格: zsb_id=' . $targetZsbId, 'INFO');
        }

        try {
            // 获取招就办价格配置
            $zsbPriceModel = D('ZsbPostPrice');
            $priceConfig = $zsbPriceModel->where([
                'zsb_id' => $targetZsbId,
                'post_id' => $postId,
                'status' => 1
            ])->find();

            if (!$priceConfig) {
                $this->ajaxReturn(['status' => 0, 'msg' => '该岗位未配置价格']);
            }

            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                $this->ajaxReturn(['status' => 0, 'msg' => '岗位不存在']);
            }

            // 获取平台费率
            $platformRate = D('ZsbPostPrice')->getPlatformRate();

            // 格式化价格数据（从分转换为元）
            $data = [
                'sale_price' => $priceConfig['sale_price'], // 分单位
                'sale_price_formatted' => round($priceConfig['sale_price'] / 100, 2), // 元单位
                'cost_price' => $priceConfig['cost_price'], // 分单位
                'cost_price_formatted' => round($priceConfig['cost_price'] / 100, 2), // 元单位
                'commission' => $priceConfig['commission'], // 分单位
                'commission_formatted' => round($priceConfig['commission'] / 100, 2), // 元单位
                'platform_rate_percent' => $platformRate * 100, // 百分比
                'post_name' => $post['job_name']
            ];

            $this->ajaxReturn(['status' => 1, 'data' => $data]);

        } catch (Exception $e) {
            \Think\Log::write('获取招就办价格配置失败：' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '获取价格配置失败']);
        }
    }

    /**
     * 获取岗位的报名费范围
     */
    public function getPostFeeRange()
    {
        $postId = I('get.post_id', 0, 'intval');

        if (!$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位ID不能为空']);
        }

        try {
            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                $this->ajaxReturn(['status' => 0, 'msg' => '岗位不存在']);
            }

            // 获取项目身份成本（最低费用）
            $projectJoinIdentity = D("ProjectJoinIdentity")->where([
                'project_id' => $post['project_id'],
                'project_post_id' => $postId,
                'project_identity_id' => 3, // 服务站身份
            ])->find();

            $baseCost = $projectJoinIdentity ? floatval($projectJoinIdentity['cost']) : 0; // 基准成本价
            $servicePrice = floatval($post['service_price']); // 最低报价
            $maxPrice = floatval($post['max_price']); // 最高报价

            // 报名费范围：岗位价格区间（最低报价 ~ 最高报价）
            $minFee = $servicePrice; // 报名费最低值为最低报价
            $maxFee = $maxPrice; // 报名费最高值为最高报价

            // 确保最低费用不超过最高费用
            if ($minFee > $maxFee) {
                $minFee = $maxFee;
            }

            $this->ajaxReturn([
                'status' => 1,
                'msg' => '获取成功',
                'data' => [
                    'min_fee' => $minFee, // 最低报价
                    'max_fee' => $maxFee, // 最高报价
                    'base_cost' => $baseCost, // 基准成本价（明确标识）
                    'service_price' => $servicePrice, // 最低报价（明确标识）
                    'max_price' => $maxPrice, // 最高报价（明确标识）
                    'post_name' => $post['job_name']
                ]
            ]);

        } catch (Exception $e) {
            \Think\Log::write('获取岗位费用范围失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '获取费用范围失败']);
        }
    }

    /**
     * 获取岗位收益计算详情（用于报名弹窗）
     */
    public function getPostProfitDetails()
    {
        $postId = I('get.post_id', 0, 'intval');
        $feeAmount = I('get.fee_amount', 0, 'floatval'); // 报名费（元单位）

        if (!$postId) {
            $this->ajaxReturn(['status' => 0, 'msg' => '岗位ID不能为空']);
        }

        try {
            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                $this->ajaxReturn(['status' => 0, 'msg' => '岗位不存在']);
            }

            // 获取基准成本价（z_project_join_identity表中服务站身份的cost）
            $projectJoinIdentity = D("ProjectJoinIdentity")->where([
                'project_id' => $post['project_id'],
                'project_post_id' => $postId,
                'project_identity_id' => 3, // 服务站身份
            ])->find();

            $baseCost = $projectJoinIdentity ? floatval($projectJoinIdentity['cost']) : 0;
            $servicePrice = floatval($post['service_price']); // 最低报价
            $maxPrice = floatval($post['max_price']); // 最高报价

            // 获取当前平台费率
            $platformRate = D('ZsbPostPrice')->getPlatformRate();

            // 计算收益（如果提供了报名费）
            $profitDetails = null;
            if ($feeAmount > 0) {
                // 计算平台服务费：超出最低报价部分 × 平台费率
                $platformFee = max(0, ($feeAmount - $servicePrice) * $platformRate);

                // 计算服务站收益：报名费 - 基准成本价 - 平台服务费
                $stationProfit = $feeAmount - $baseCost - $platformFee;

                // 确保收益不为负数
                $stationProfit = max(0, $stationProfit);

                $profitDetails = [
                    'fee_amount' => $feeAmount,
                    'base_cost' => $baseCost,
                    'platform_fee' => round($platformFee, 2),
                    'station_profit' => round($stationProfit, 2),
                    'platform_rate_percent' => round($platformRate * 100, 1)
                ];
            }

            $this->ajaxReturn([
                'status' => 1,
                'msg' => '获取成功',
                'data' => [
                    'post_id' => $postId,
                    'post_name' => $post['job_name'],
                    'project_name' => $post['project_name'] ?? '',
                    'base_cost' => $baseCost, // 基准成本价（z_project_join_identity.cost）
                    'service_price' => $servicePrice, // 最低报价
                    'max_price' => $maxPrice, // 最高报价
                    'min_fee' => $servicePrice, // 报名费最低值（最低报价）
                    'max_fee' => $maxPrice, // 报名费最高值（最高报价）
                    'platform_rate' => $platformRate, // 平台费率
                    'platform_rate_percent' => round($platformRate * 100, 1), // 平台费率百分比
                    'profit_details' => $profitDetails, // 收益计算详情
                    'price_range' => $servicePrice . ' - ' . $maxPrice . ' 元', // 价格区间（用于显示）
                    'fee_range' => $servicePrice . ' - ' . $maxPrice . ' 元', // 报名费范围（最低报价~最高报价）
                    'formula' => [
                        'platform_fee' => '平台服务费 = max(0, (报名费 - 最低报价) × 平台费率)',
                        'station_profit' => '服务站收益 = 报名费 - 基准成本价 - 平台服务费'
                    ]
                ]
            ]);

        } catch (Exception $e) {
            \Think\Log::write('获取岗位收益详情失败: ' . $e->getMessage(), 'ERROR');
            $this->ajaxReturn(['status' => 0, 'msg' => '获取收益详情失败']);
        }
    }

    /**
     * 验证报名费范围
     * @param float $feeAmount 报名费（元单位）
     * @param int $postId 岗位ID
     * @return array
     */
    private function validateFeeRange($feeAmount, $postId)
    {
        try {
            // 获取岗位信息
            $post = D('ProjectPost')->where(['id' => $postId])->find();
            if (!$post) {
                return ['success' => false, 'message' => '岗位不存在'];
            }

            // 获取项目身份成本（最低费用）
            $projectJoinIdentity = D("ProjectJoinIdentity")->where([
                'project_id' => $post['project_id'],
                'project_post_id' => $postId,
                'project_identity_id' => 3, // 服务站身份
            ])->find();

            $baseCost = $projectJoinIdentity ? floatval($projectJoinIdentity['cost']) : 0; // 基准成本价
            $servicePrice = floatval($post['service_price']); // 最低报价
            $maxPrice = floatval($post['max_price']); // 最高报价

            // 报名费范围：岗位价格区间（最低报价 ~ 最高报价）
            $minFee = $servicePrice; // 报名费最低值为最低报价
            $maxFee = $maxPrice; // 报名费最高值为最高报价

            // 确保最低费用不超过最高费用
            if ($minFee > $maxFee) {
                $minFee = $maxFee;
            }

            // 验证报名费范围
            if ($feeAmount < $minFee) {
                return ['success' => false, 'message' => '报名费不能低于最低报价：' . $minFee . '元'];
            }

            if ($feeAmount > $maxFee) {
                return ['success' => false, 'message' => '报名费不能超过最高报价：' . $maxFee . '元'];
            }

            return ['success' => true, 'message' => '验证通过'];

        } catch (Exception $e) {
            \Think\Log::write('验证报名费范围失败: ' . $e->getMessage(), 'ERROR');
            return ['success' => false, 'message' => '验证失败，请稍后重试'];
        }
    }

    /**
     * 计算真正的服务站收益（不包含招就办收益）
     * @param array $order 订单信息
     * @return float 服务站收益（元单位）
     */
    private function calculateRealStationProfit($order)
    {
        if (empty($order['zsb_id']) || $order['zsb_id'] <= 0) {
            return 0;
        }

        try {
            // 获取招就办价格配置
            $zsbPriceConfig = D('ZsbPostPrice')->where([
                'zsb_id' => $order['zsb_id'],
                'post_id' => $order['post_id'],
                'status' => 1
            ])->find();

            if (!$zsbPriceConfig) {
                return 0;
            }

            // 获取基准成本价（z_project_join_identity表中服务站身份的cost）
            $baseCost = D('ProjectJoinIdentity')->getBaseCostByPostId($order['post_id']); // 元单位

            // 计算服务站收益 = cost_price（分） - 基准成本价（元转分）
            $baseCostCents = $baseCost * 100; // 将元转换为分
            $stationProfit = $zsbPriceConfig['cost_price'] - $baseCostCents;

            // 确保收益不为负数
            if ($stationProfit < 0) {
                $stationProfit = 0;
            }

            // 转换为元单位
            return round($stationProfit / 100, 2);

        } catch (Exception $e) {
            \Think\Log::write('计算真正服务站收益失败: ' . $e->getMessage(), 'ERROR');
            return 0;
        }
    }

    /**
     * 获取有上架岗位的项目列表
     * @return array
     */
    private function getProjectsWithAvailablePosts()
    {
        // 使用子查询获取有上架岗位的项目ID
        $projectIds = D("ProjectPost")
            ->where(['status' => 1])
            ->group('project_id')
            ->getField('project_id', true);

        if (empty($projectIds)) {
            return [];
        }

        // 获取这些项目的详细信息
        return D("Project")
            ->where([
                'status' => 1,
                'id' => ['in', $projectIds]
            ])
            ->field('id, name')
            ->select();
    }
}
